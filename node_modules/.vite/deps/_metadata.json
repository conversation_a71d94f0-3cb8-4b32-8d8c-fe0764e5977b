{"hash": "1782a926", "configHash": "dbb4832e", "lockfileHash": "b9a73ea9", "browserHash": "fa0f8d2c", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a61d672c", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "32fc9ceb", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "49d6ad21", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "e0d8c5d5", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "dc6a3589", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "d62a5252", "needsInterop": false}, "@radix-ui/react-icons": {"src": "../../@radix-ui/react-icons/dist/react-icons.esm.js", "file": "@radix-ui_react-icons.js", "fileHash": "d9ba5d6a", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "05b43e0f", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "92fb9d53", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "fa2953cb", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "7961fde7", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "0a042b4d", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "59cd01e6", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "287770c4", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "66df160b", "needsInterop": false}, "@tanstack/react-query-devtools": {"src": "../../@tanstack/react-query-devtools/build/modern/index.js", "file": "@tanstack_react-query-devtools.js", "fileHash": "c783cf50", "needsInterop": false}, "@tanstack/react-table": {"src": "../../@tanstack/react-table/build/lib/index.mjs", "file": "@tanstack_react-table.js", "fileHash": "08d8ad51", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "bc96d035", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "13b30748", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "cb2093e8", "needsInterop": false}, "i18next": {"src": "../../i18next/dist/esm/i18next.js", "file": "i18next.js", "fileHash": "6725b791", "needsInterop": false}, "i18next-browser-languagedetector": {"src": "../../i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js", "file": "i18next-browser-languagedetector.js", "fileHash": "34a33832", "needsInterop": false}, "i18next-http-backend": {"src": "../../i18next-http-backend/esm/index.js", "file": "i18next-http-backend.js", "fileHash": "9d19e0e0", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b2cc7095", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "06b92283", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "31a9a777", "needsInterop": true}, "react-error-boundary": {"src": "../../react-error-boundary/dist/react-error-boundary.development.esm.js", "file": "react-error-boundary.js", "fileHash": "265464bd", "needsInterop": false}, "react-helmet-async": {"src": "../../react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "87d0f56b", "needsInterop": false}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "54df329d", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "03695d5a", "needsInterop": false}, "react-i18next": {"src": "../../react-i18next/dist/es/index.js", "file": "react-i18next.js", "fileHash": "aac30f66", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "0dec582d", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "a8f4c9e2", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "b3f35b4d", "needsInterop": false}, "use-debounce": {"src": "../../use-debounce/dist/index.module.js", "file": "use-debounce.js", "fileHash": "2ef0cd69", "needsInterop": false}, "zod": {"src": "../../zod/lib/index.mjs", "file": "zod.js", "fileHash": "9af4bbf8", "needsInterop": false}, "input-otp": {"src": "../../input-otp/dist/index.mjs", "file": "input-otp.js", "fileHash": "2cb55705", "needsInterop": false}}, "chunks": {"H3J5RHJ6-A3MGVV5M": {"file": "H3J5RHJ6-A3MGVV5M.js"}, "XV5R7GGF-E4C7LA2E": {"file": "XV5R7GGF-E4C7LA2E.js"}, "chunk-YXIEM3MW": {"file": "chunk-YXIEM3MW.js"}, "browser-ponyfill-V2J5XIZO": {"file": "browser-ponyfill-V2J5XIZO.js"}, "chunk-UV7J6DXB": {"file": "chunk-UV7J6DXB.js"}, "chunk-6ZMM2PAV": {"file": "chunk-6ZMM2PAV.js"}, "chunk-3DMX7Q7X": {"file": "chunk-3DMX7Q7X.js"}, "chunk-UK5NNNGH": {"file": "chunk-UK5NNNGH.js"}, "chunk-MBBQPERT": {"file": "chunk-MBBQPERT.js"}, "chunk-YABUFNLM": {"file": "chunk-YABUFNLM.js"}, "chunk-KSEXWREU": {"file": "chunk-KSEXWREU.js"}, "chunk-OKX5DAGM": {"file": "chunk-OKX5DAGM.js"}, "chunk-SGSAFNPX": {"file": "chunk-SGSAFNPX.js"}, "chunk-CI2I72SZ": {"file": "chunk-CI2I72SZ.js"}, "chunk-OJ2TJDIO": {"file": "chunk-OJ2TJDIO.js"}, "chunk-JE5SMWTQ": {"file": "chunk-JE5SMWTQ.js"}, "chunk-WU7ET7AM": {"file": "chunk-WU7ET7AM.js"}, "chunk-NRN5YYFF": {"file": "chunk-NRN5YYFF.js"}, "chunk-AZCBCMZO": {"file": "chunk-AZCBCMZO.js"}, "chunk-BTIBV3P6": {"file": "chunk-BTIBV3P6.js"}, "chunk-OD433RWB": {"file": "chunk-OD433RWB.js"}, "chunk-LSQNWB54": {"file": "chunk-LSQNWB54.js"}, "chunk-H5AYEWDG": {"file": "chunk-H5AYEWDG.js"}, "chunk-T2SWDQEL": {"file": "chunk-T2SWDQEL.js"}, "chunk-DKHUMOWT": {"file": "chunk-DKHUMOWT.js"}, "chunk-KBTYAULA": {"file": "chunk-KBTYAULA.js"}, "chunk-QCHXOAYK": {"file": "chunk-QCHXOAYK.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}