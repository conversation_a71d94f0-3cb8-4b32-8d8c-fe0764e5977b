import { useMutation } from '@tanstack/react-query';
import { client } from '../../../lib/axiosClient';

const checkOtp = async (data: { email: string; otp: string }) => {
  const response = await client.post('/admin/auth/check-otp', data);
  return response.data;
};

export function useCheckOtp() {
  const mutation = useMutation({
    mutationFn: checkOtp,
    onError: (error) => {
      console.error('Check OTP failed:', error);
    }
  });

  return mutation;
}
