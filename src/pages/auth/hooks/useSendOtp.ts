import { useMutation } from '@tanstack/react-query';
import { client } from '../../../lib/axiosClient';

const sendOtp = async (data: { email: string }) => {
  const response = await client.post('/admin/auth/send-otp', data);
  return response.data;
};

export function useSendOtp() {
  const mutation = useMutation({
    mutationFn: sendOtp,
    onError: (error) => {
      console.error('Send OTP failed:', error);
    }
  });

  return mutation;
}
