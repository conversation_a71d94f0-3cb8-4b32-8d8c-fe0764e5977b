import ChangePasswordPage from '@/pages/auth/change-password';
import ResetPage from '@/pages/auth/reset-form';
import NotFound from '@/pages/not-found';
import { Suspense, lazy } from 'react';
import { Navigate, Outlet, useRoutes } from 'react-router-dom';

const DashboardLayout = lazy(
  () => import('@/components/layout/dashboard-layout')
);
const SignInPage = lazy(() => import('@/pages/auth/signin'));
const ForgotPasswordPage = lazy(() => import('@/pages/auth/forgot-password'));
const OtpInputPage = lazy(() => import('@/pages/auth/otp-input'));
const DashboardPage = lazy(() => import('@/pages/dashboard'));

export default function AppRouter() {
  const dashboardRoutes = [
    {
      path: '/',
      element: (
        <DashboardLayout>
          <Suspense>
            <Outlet />
          </Suspense>
        </DashboardLayout>
      ),
      children: [
        {
          element: <DashboardPage />,
          index: true
        },
        // {
        //   path: 'countries',
        //   element: <CountryPage />
        // },
        // {
        //   path: 'cities',
        //   element: <CityPage />
        // },
        // {
        //   path: 'areas',
        //   element: <AreaPage />
        // },
        // {
        //   path: 'drivers',
        //   element: <DriverPage />
        // },
        // {
        //   path: 'orders',
        //   element: <OrdersPage />
        // },
        // {
        //   path: 'roles',
        //   element: <RolesPage />
        // },
        // {
        //   path: 'managers',
        //   element: <ManagerPage />
        // },
        // {
        //   path: 'restaurants',
        //   element: <RestaurantPage />
        // },
        {
          path: '/change-password',
          element: <ChangePasswordPage />,
          index: true
        }
      ]
    }
  ];

  const publicRoutes = [
    {
      path: '/login',
      element: <SignInPage />,
      index: true
    },
    {
      path: '/forgot-password',
      element: <ForgotPasswordPage />
    },
    {
      path: '/otp-input',
      element: <OtpInputPage />
    },
    {
      path: '/reset-password',
      element: <ResetPage />
    },
    {
      path: '/404',
      element: <NotFound />
    },
    {
      path: '*',
      element: <Navigate to="/404" replace />
    }
  ];

  const routes = useRoutes([...dashboardRoutes, ...publicRoutes]);

  return routes;
}
