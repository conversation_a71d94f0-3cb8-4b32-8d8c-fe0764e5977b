import { useMutation } from '@tanstack/react-query';
import { client } from '../../../lib/axiosClient';

const resetPassword = async (data: { password: string, password_confirmation: string }) => {
    const reset_token = localStorage.getItem("reset_token")
    const response = await client.patch('/admin/auth/reset-password', data ,{
        headers: {
            Authorization: `Bearer ${reset_token}`
        }
    });
    return response.data;
};

export function useResetPassword() {
    const mutation = useMutation({
        mutationFn: resetPassword,
        onError: (error) => {
            console.error('Reset password failed:', error);
        }
    });

    return mutation;
}
