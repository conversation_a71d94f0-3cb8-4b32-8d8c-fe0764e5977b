import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot
} from '@/components/ui/input-otp';
import { useRouter } from '@/routes/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import toast from 'react-hot-toast';
import { useCheckOtp } from '../../hooks/useCheckOtp';
import { useLocation } from 'react-router-dom';
import { useEffect } from 'react';

const formSchema = z.object({
  otp: z.string().min(6, { message: 'OTP must be 6 digits' })
});

type OtpFormValue = z.infer<typeof formSchema>;

export default function OtpForm() {
  const router = useRouter();
  const location = useLocation();
  const { mutate: checkOtp, isPending } = useCheckOtp();

  // Get email from navigation state
  const email = location.state?.email;

  const form = useForm<OtpFormValue>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      otp: ''
    }
  });

  useEffect(() => {
    // Redirect to forgot password if no email is provided
    if (!email) {
      router.push('/forgot-password');
    }
  }, [email, router]);

  const onSubmit = async (data: OtpFormValue) => {
    if (!email) {
      toast.error('Email is required');
      return;
    }

    checkOtp(
      { email, otp: data.otp },
      {
        onSuccess: (data) => {
          toast.success('OTP verified successfully');
          // Navigate to login or password reset page
          localStorage.setItem('reset_token', data.data.token)
          router.push('/reset-password');
        },
        onError: (error: any) => {
          toast.error(error.response?.data?.message || 'Invalid OTP');
        }
      }
    );
  };

  if (!email) {
    return null; // Will redirect in useEffect
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="w-full space-y-6"
      >
        <FormField
          control={form.control}
          name="otp"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Enter OTP</FormLabel>
              <FormControl>
                <InputOTP
                  maxLength={6}
                  disabled={isPending}
                  {...field}
                >
                  <InputOTPGroup>
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                    <InputOTPSlot index={4} />
                    <InputOTPSlot index={5} />
                  </InputOTPGroup>
                </InputOTP>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-4">
            We sent an OTP to <strong>{email}</strong>
          </p>
        </div>

        <Button disabled={isPending} className="w-full" type="submit">
          {isPending ? 'Verifying...' : 'Verify OTP'}
        </Button>
      </form>
    </Form>
  );
}
