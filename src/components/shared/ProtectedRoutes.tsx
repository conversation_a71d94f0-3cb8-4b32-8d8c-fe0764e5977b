import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const ProtectedRoute = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const token = localStorage.getItem('token');

    // Define public routes that don't require authentication
    const publicRoutes = ['/login', '/forgot-password', '/otp-input','/reset-password', '/404'];
    const isPublicRoute = publicRoutes.includes(location.pathname);

    if (!token && !isPublicRoute) {
      navigate('/login');
    }
  }, [navigate, location.pathname]);

  return <>{children}</>;
};

export default ProtectedRoute;

// Usage example
// In your routing setup:
// <Routes>
//   <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
// </Routes>
